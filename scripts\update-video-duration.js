#!/usr/bin/env node

/**
 * 视频时长配置更新工具
 * 用于快速修改系统的视频时长限制
 */

const fs = require('fs');
const path = require('path');

// 配置文件路径
const CONFIG_FILE = path.join(__dirname, '../server/config/index.js');

/**
 * 更新视频时长配置
 * @param {number} minDuration - 最小时长（秒）
 * @param {number} maxDuration - 最大时长（秒）
 */
function updateVideoDurationConfig(minDuration, maxDuration) {
  try {
    // 验证参数
    if (!minDuration || !maxDuration) {
      throw new Error('最小时长和最大时长都必须提供');
    }

    if (minDuration < 1 || maxDuration < 1) {
      throw new Error('时长必须大于0秒');
    }

    if (minDuration >= maxDuration) {
      throw new Error('最小时长必须小于最大时长');
    }

    if (maxDuration > 3600) {
      throw new Error('最大时长不能超过3600秒（1小时）');
    }

    // 读取配置文件
    let configContent = fs.readFileSync(CONFIG_FILE, 'utf8');

    // 使用正则表达式替换视频时长配置
    const videoDurationRegex = /videoDuration:\s*\{[^}]*\}/s;
    const newVideoDurationConfig = `videoDuration: {
        minDuration: ${minDuration},    // 最小时长（秒）
        maxDuration: ${maxDuration},    // 最大时长（秒）
        // 说明：修改此处的值会影响整个系统的视频时长限制
        // 包括：相机录制、视频上传验证、前端显示等
        description: '视频时长控制的统一配置，修改此处会影响整个系统'
      }`;

    if (videoDurationRegex.test(configContent)) {
      configContent = configContent.replace(videoDurationRegex, newVideoDurationConfig);
    } else {
      throw new Error('未找到视频时长配置段落');
    }

    // 写回配置文件
    fs.writeFileSync(CONFIG_FILE, configContent, 'utf8');

    console.log('✅ 视频时长配置更新成功！');
    console.log(`   最小时长: ${minDuration}秒`);
    console.log(`   最大时长: ${maxDuration}秒`);
    console.log('');
    console.log('⚠️  请重启服务器使配置生效');

  } catch (error) {
    console.error('❌ 更新配置失败:', error.message);
    process.exit(1);
  }
}

/**
 * 显示当前配置
 */
function showCurrentConfig() {
  try {
    const configContent = fs.readFileSync(CONFIG_FILE, 'utf8');
    const videoDurationMatch = configContent.match(/videoDuration:\s*\{[^}]*\}/s);
    
    if (videoDurationMatch) {
      console.log('📋 当前视频时长配置:');
      console.log(videoDurationMatch[0]);
    } else {
      console.log('❌ 未找到视频时长配置');
    }
  } catch (error) {
    console.error('❌ 读取配置失败:', error.message);
  }
}

/**
 * 显示帮助信息
 */
function showHelp() {
  console.log('🎥 视频时长配置更新工具');
  console.log('');
  console.log('用法:');
  console.log('  node update-video-duration.js <最小时长> <最大时长>');
  console.log('  node update-video-duration.js show                    # 显示当前配置');
  console.log('  node update-video-duration.js help                    # 显示帮助');
  console.log('');
  console.log('示例:');
  console.log('  node update-video-duration.js 3 10    # 设置最小3秒，最大10秒');
  console.log('  node update-video-duration.js 2 5     # 设置最小2秒，最大5秒');
  console.log('');
  console.log('注意:');
  console.log('  - 时长单位为秒');
  console.log('  - 最小时长必须小于最大时长');
  console.log('  - 修改后需要重启服务器');
}

// 主程序
function main() {
  const args = process.argv.slice(2);

  if (args.length === 0 || args[0] === 'help') {
    showHelp();
    return;
  }

  if (args[0] === 'show') {
    showCurrentConfig();
    return;
  }

  if (args.length !== 2) {
    console.error('❌ 参数错误，请提供最小时长和最大时长');
    showHelp();
    process.exit(1);
  }

  const minDuration = parseInt(args[0]);
  const maxDuration = parseInt(args[1]);

  if (isNaN(minDuration) || isNaN(maxDuration)) {
    console.error('❌ 时长必须是数字');
    process.exit(1);
  }

  updateVideoDurationConfig(minDuration, maxDuration);
}

// 运行主程序
main();
