/**
 * 视频时长管理工具类
 * 统一管理视频时长控制逻辑，避免代码重复
 */

class VideoDurationManager {
  constructor() {
    // 默认配置 - 与服务器端配置保持一致
    this.defaultSettings = {
      min_duration: 2,
      max_duration: 5
    };
    
    this.settings = null;
  }

  /**
   * 加载视频时长设置
   * @returns {Promise<Object>} 视频时长设置
   */
  async loadSettings() {
    try {
      const app = getApp();
      const res = await app.request({
        url: "/api/video-settings",
        method: "GET",
      });

      if (res.data && res.data.success) {
        this.settings = res.data.data;
        console.log("视频时长设置加载成功:", this.settings);
        return this.settings;
      } else {
        throw new Error('API返回失败');
      }
    } catch (error) {
      console.error("加载视频时长设置失败:", error);
      // 使用默认设置
      this.settings = { ...this.defaultSettings };
      return this.settings;
    }
  }

  /**
   * 获取当前设置
   * @returns {Object} 当前的视频时长设置
   */
  getSettings() {
    return this.settings || { ...this.defaultSettings };
  }

  /**
   * 验证视频时长
   * @param {number} duration - 视频时长（秒）
   * @param {Object} customSettings - 自定义设置（可选）
   * @returns {Object} 验证结果 {isValid: boolean, message: string}
   */
  validateDuration(duration, customSettings = null) {
    const settings = customSettings || this.getSettings();
    
    if (duration < settings.min_duration) {
      return {
        isValid: false,
        message: `录制的视频时长为${duration}秒，不能少于${settings.min_duration}秒。请重新录制符合要求的视频。`
      };
    }
    
    if (duration > settings.max_duration) {
      return {
        isValid: false,
        message: `录制的视频时长为${duration}秒，不能超过${settings.max_duration}秒。请重新录制符合要求的视频。`
      };
    }
    
    return { isValid: true };
  }

  /**
   * 显示时长验证错误提示
   * @param {Object} validationResult - 验证结果
   */
  showValidationError(validationResult) {
    if (!validationResult.isValid) {
      wx.showModal({
        title: "视频时长不符合要求",
        content: validationResult.message,
        showCancel: false,
        confirmText: "知道了",
      });
    }
  }

  /**
   * 获取最大时长
   * @returns {number} 最大时长（秒）
   */
  getMaxDuration() {
    const settings = this.getSettings();
    return settings.max_duration;
  }

  /**
   * 获取最小时长
   * @returns {number} 最小时长（秒）
   */
  getMinDuration() {
    const settings = this.getSettings();
    return settings.min_duration;
  }

  /**
   * 处理视频时长（四舍五入）
   * @param {number} duration - 原始时长
   * @returns {number} 处理后的时长
   */
  processDuration(duration) {
    if (duration !== undefined && duration !== null && duration > 0) {
      return Math.round(duration);
    }
    return 0;
  }
}

// 创建单例实例
const videoDurationManager = new VideoDurationManager();

module.exports = videoDurationManager;
