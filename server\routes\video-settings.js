const express = require('express');
const router = express.Router();
const config = require('../config');
const { requireAuth } = require('../middleware/auth');

// 获取视频时长设置
router.get('/', requireAuth, async (req, res) => {
  try {
    // 从配置文件读取视频时长设置
    const settings = {
      min_duration: config.get('videoDuration.minDuration', 2),
      max_duration: config.get('videoDuration.maxDuration', 5)
    };

    res.json({
      success: true,
      data: settings
    });
  } catch (error) {
    console.error('获取视频时长设置错误:', error);
    res.status(500).json({
      success: false,
      message: '获取视频时长设置失败'
    });
  }
});

// 更新视频时长设置
router.put('/', requireAuth, async (req, res) => {
  try {
    const { min_duration, max_duration } = req.body;

    // 验证输入
    if (!min_duration || !max_duration) {
      return res.status(400).json({
        success: false,
        message: '最小时长和最大时长不能为空'
      });
    }

    if (min_duration < 1 || max_duration < 1) {
      return res.status(400).json({
        success: false,
        message: '时长必须大于0秒'
      });
    }

    if (min_duration >= max_duration) {
      return res.status(400).json({
        success: false,
        message: '最小时长必须小于最大时长'
      });
    }

    if (max_duration > 3600) {
      return res.status(400).json({
        success: false,
        message: '最大时长不能超过3600秒（1小时）'
      });
    }

    // 更新配置文件中的设置
    config.set('videoDuration.minDuration', min_duration);
    config.set('videoDuration.maxDuration', max_duration);

    res.json({
      success: true,
      message: '视频时长设置更新成功',
      data: {
        min_duration,
        max_duration
      }
    });
  } catch (error) {
    console.error('更新视频时长设置错误:', error);
    res.status(500).json({
      success: false,
      message: '更新视频时长设置失败'
    });
  }
});

module.exports = router;
