# 视频时长控制配置说明

## 概述
本系统的视频时长控制采用统一配置管理，通过 `utils/video-duration-manager.js` 工具类统一处理所有视频时长相关逻辑。

## 配置位置
**主配置文件**: `server/config/index.js`

```javascript
// 视频时长控制配置 - 统一配置入口
videoDuration: {
  minDuration: 2,    // 最小时长（秒）
  maxDuration: 5,    // 最大时长（秒）
  description: '视频时长控制的统一配置，修改此处会影响整个系统'
}
```

## 如何修改视频时长限制

### 1. 修改配置文件
只需要修改 `server/config/index.js` 中的 `videoDuration` 配置：

```javascript
videoDuration: {
  minDuration: 3,    // 改为3秒最小时长
  maxDuration: 10,   // 改为10秒最大时长
}
```

### 2. 重启服务器
修改配置后需要重启服务器使配置生效。

## 统一管理工具
**视频时长管理器**: `utils/video-duration-manager.js`

这个工具类统一处理：
- 配置加载
- 时长验证
- 错误提示
- 默认值管理

## 影响范围
修改配置会影响以下功能：

1. **相机录制页面** - 录制时长限制和验证
2. **反馈单页面** - 视频上传验证
3. **服务器端验证** - API返回的时长限制

## 配置传递流程

```
server/config/index.js (主配置)
    ↓
server/routes/video-settings.js (API接口)
    ↓
utils/video-duration-manager.js (统一管理)
    ↓
前端页面应用配置
```

## 代码优化
- ✅ 删除了重复的验证逻辑
- ✅ 统一了默认值管理
- ✅ 简化了配置加载流程
- ✅ 减少了代码冗余

## 相关文件列表
- `server/config/index.js` - 主配置文件
- `server/routes/video-settings.js` - 视频设置API
- `utils/video-duration-manager.js` - 统一管理工具
- `subpackages/feedback-management/feedback/feedback.js` - 反馈页面
- `subpackages/feedback-management/camera-capture/camera-capture.js` - 相机录制页面
