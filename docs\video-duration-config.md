# 视频时长控制配置说明

## 概述
本系统的视频时长控制采用统一配置管理，所有相关的时长限制都从一个中心配置点读取。

## 配置位置
**主配置文件**: `server/config/index.js`

```javascript
// 视频时长控制配置 - 统一配置入口
videoDuration: {
  minDuration: 2,    // 最小时长（秒）
  maxDuration: 5,    // 最大时长（秒）
  // 说明：修改此处的值会影响整个系统的视频时长限制
  // 包括：相机录制、视频上传验证、前端显示等
  description: '视频时长控制的统一配置，修改此处会影响整个系统'
}
```

## 如何修改视频时长限制

### 1. 修改配置文件
只需要修改 `server/config/index.js` 中的 `videoDuration` 配置：

```javascript
videoDuration: {
  minDuration: 3,    // 改为3秒最小时长
  maxDuration: 10,   // 改为10秒最大时长
}
```

### 2. 重启服务器
修改配置后需要重启服务器使配置生效。

## 影响范围
修改配置会影响以下功能：

1. **相机录制页面** (`subpackages/feedback-management/camera-capture/camera-capture.js`)
   - 录制时长限制
   - 自动停止录制
   - 时长验证提示

2. **反馈单页面** (`subpackages/feedback-management/feedback/feedback.js`)
   - 视频上传验证
   - 时长检查提示
   - 相机录制结果验证

3. **服务器端验证** (`server/routes/video-settings.js`)
   - API返回的时长限制
   - 后端验证逻辑

## 配置传递流程

```
server/config/index.js (主配置)
    ↓
server/routes/video-settings.js (API接口)
    ↓
前端页面请求 /api/video-settings
    ↓
前端页面应用配置
```

## 默认值说明
- **最小时长**: 2秒 - 确保录制的视频有足够的内容
- **最大时长**: 5秒 - 控制文件大小和上传时间
- **备用默认值**: 当API请求失败时，前端会使用相同的默认值

## 注意事项
1. 修改配置后必须重启服务器
2. 前端的默认值应该与服务器配置保持一致
3. 建议在测试环境先验证配置修改的效果
4. 时长限制会影响用户体验，建议根据实际需求调整

## 相关文件列表
- `server/config/index.js` - 主配置文件
- `server/routes/video-settings.js` - 视频设置API
- `subpackages/feedback-management/feedback/feedback.js` - 反馈页面
- `subpackages/feedback-management/camera-capture/camera-capture.js` - 相机录制页面
